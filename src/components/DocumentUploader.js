import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { pick, types } from '@react-native-documents/picker';
import { useDispatch } from 'react-redux';
import { GenerateImageLink } from '../redux/features/mainSlice';
import colors from '../assets/colors';
import icons from '../assets/icons';

const DocumentUploader = ({ onUploadSuccess, label = "Upload Document" }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = React.useState(false);
  const [fileName, setFileName] = React.useState(null);

  const handleFilePick = async () => {
    try {
      const result = await pick({
        allowMultiSelection: false,
        // type: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        type: [types.pdf, types.docx],
      });

      if (result && result.length > 0) {
        const file = result[0];
        setFileName(file.name);
        await uploadFile(file);
      }
    } catch (err) {
      if (err.message.includes('User canceled')) {
        console.log('User canceled the document picker');
      } else {
        console.error('Document picker error:', err);
        Alert.alert('Error', 'Could not pick the document.');
      }
    }
  };

  const uploadFile = async (file) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('profile_image', {
        uri: file.uri,
        name: file.name,
        type: file.type,
      });

      const response = await dispatch(GenerateImageLink(formData));
      console.log('Upload response:', JSON.stringify(response));

      if (response?.payload?.success) {
        onUploadSuccess(response?.payload?.data?.profile_image?.s3Url);
      } else {
        Alert.alert('Upload Failed', 'Something went wrong while uploading the document.');
      }

    } catch (err) {
      console.error('Upload error:', err);
      Alert.alert('Upload Failed', 'Something went wrong while uploading the document.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={handleFilePick} style={styles.uploadButton}>
        <Image source={icons.download} style={styles.icon} />
        <Text style={styles.buttonText}>{label}</Text>
      </TouchableOpacity>

      {loading && <ActivityIndicator size="small" color={colors.primary} style={styles.loader} />}
      {fileName && <Text style={styles.fileName}>{fileName}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.inputBg,
    padding: 12,
    borderRadius: 8,
    alignSelf: 'center',
    width: '100%',
    justifyContent: "center"
  },
  buttonText: {
    color: '#888',
    marginLeft: 10,
    fontSize: 16,
    textAlign: 'center',
  },
  icon: {
    width: 18,
    height: 18,
    tintColor: '#888',
  },
  loader: {
    marginTop: 10,
  },
  fileName: {
    marginTop: 10,
    fontSize: 14,
    color: '#555',
  },
});

export default DocumentUploader;
