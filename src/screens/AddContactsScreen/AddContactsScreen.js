import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from "react-native";
import Header from "../../components/Header";
import SearchBar from "../../components/SearchBar";
import ContactCard from "../../components/ContactCard";
import SortOptionsBox from "../../components/SortOptionsBox";
import icons from "../../assets/icons";
import colors from "../../assets/colors";
import { filterOptions, sortOptions } from "../../utils/constants";
import { useDispatch, useSelector } from "react-redux";
import { showToast } from "../../utils/toastConfig";
import {
  setTagsData,
  setProfileContactsData,
} from "../../redux/features/mainSlice";
import MyText from "../../components/MyText";
import {
  getContacts,
  getProfileContacts,
} from "../../redux/features/contactSlice";
import { PrimaryButton } from "../../components/Button";
import AppLoader from "../../components/AppLoader";
import { addContactsToProfile } from "../../redux/features/SharingProfileSlice";

const formatContacts = (data = []) =>
  data.map((contact) => {
    // console.log("🚀 ~ data.map ~ contact:", contact);
    return {
      id: contact._id,
      name: `${contact.firstName || ""} ${contact.middleName || ""} ${
        contact.lastName || ""
      }`.trim(),
      phone: contact.phoneNumbers?.[0]?.number || "",
      is_favorite: contact.is_favorite,
      imgUrl: contact.profile_image || "",
    };
  });

const getContactsList = (route, contacts, profileContactsData) => {
  if (route?.params?.fromProfile && profileContactsData?.data?.result) {
    return formatContacts(profileContactsData.data.result);
  }
  if (contacts?.data?.result) {
    return formatContacts(contacts?.data?.result);
  }
  return [];
};

const getInitialSelectedContacts = (
  route,
  tagsData,
  selectedProfileContactsData
) => {
  if (tagsData && !route?.params?.fromProfile) {
    return tagsData.contacts;
  }
  if (route?.params?.fromProfile && selectedProfileContactsData) {
    return (selectedProfileContactsData || []).filter(
      (contact) => contact && Object.keys(contact).length > 0
    );
  }
  if (route?.params?.selectedContacts) {
    return route.params.selectedContacts;
  }
  return [];
};

const AddContactsScreen = ({ navigation, route }) => {
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [activeSort, setActiveSort] = useState("az");
  const [activeFilter, setActiveFilter] = useState(null);
  const [isSortBoxVisible, setSortBoxVisible] = useState(false);
  const [isFilterBoxVisible, setFilterBoxVisible] = useState(false);

  const contactsDataState = useSelector(
    (state) => state.contactSlice.getContacts
  );
  const { data: contacts, loading: contactsLoading } = contactsDataState;

  const profileContacts = useSelector(
    (state) => state.contactSlice.getProfileContacts
  );
  const { data: profileContactsData, loading } = profileContacts;
  const selectedProfileContactsData = useSelector(
    (state) => state.mainSlice.profileContactsData
  );
  const tagsData = useSelector((state) => state.mainSlice.tagsData);
  const dispatch = useDispatch();
  const [contactsList, setContactsList] = useState([]);

  // Fetch contacts based on navigation source
  useEffect(() => {
    if (route?.params?.fromProfile) {
      dispatch(getProfileContacts());
    } else {
      dispatch(getContacts());
    }
  }, [route?.params, dispatch]);

  // Update contactsList when contacts or profileContacts change
  useEffect(() => {
    const tempContacts = getContactsList(route, contacts, profileContactsData);
    console.log("🚀 ~ useEffect ~ tempContacts:", tempContacts);
    setContactsList(tempContacts);
  }, [contacts, profileContactsData, route?.params]);

  // Sync selectedContacts with Redux or route params
  useEffect(() => {
    setSelectedContacts(
      getInitialSelectedContacts(route, tagsData, selectedProfileContactsData)
    );
  }, [tagsData, selectedProfileContactsData, route?.params]);

  // Toggle contact selection
  const toggleSelect = (item) => {
    console.log("🚀 ~ toggleSelect ~ item:", item);
    if (!item?.id) return;
    setSelectedContacts((prev) => {
      console.log("🚀 ~ toggleSelect ~ prev:", prev);
      return prev.some((contact) => contact.id === item.id)
        ? prev.filter((contact) => contact.id !== item.id)
        : [...prev, item];
    });
  };
  console.log(selectedContacts, "selectedContacts");

  const handleAddContactToProfile = async () => {
    if (!selectedContacts.length) {
      showToast("error", "Error", "Please select at least one contact.");
      return;
    }
    const addApiBody = {
      addInProfile: selectedContacts.map((contact) => ({
        memberId: contact.id,
      })),
    };
    try {
      const response = await dispatch(
        addContactsToProfile(route?.params?.fromProfile, addApiBody)
      );

      const isSuccess =
        response.success || (response.payload && response.payload.success);
      showToast(
        isSuccess ? "success" : "error",
        response?.payload?.message ||
          response?.message ||
          (isSuccess
            ? "Contacts added successfully."
            : "Failed to add contacts.")
      );
      navigation.goBack();
    } catch (error) {
      showToast(
        "error",
        error?.message || "Something went wrong while adding contacts."
      );
    }
  };

  // Handle add to tag/profile
  const handleAddToTag = () => {
    if (!selectedContacts.length) {
      showToast(
        "error",
        "Please select at least one contact to add to the tag."
      );
      return;
    }
    try {
      if (typeof route?.params?.fromProfile === "string") {
        // handleAddContactToProfile();
        // navigation.navigate("EditSharingProfileScreen", {
        //   profileId: route?.params?.fromProfile,
        //   selectedContacts: { selectedContacts },
        //   merge: true,
        // });
        dispatch(setProfileContactsData(selectedContacts));
        navigation.goBack();
      } else if (route?.params?.fromProfile) {
        dispatch(setProfileContactsData(selectedContacts));
        navigation.goBack();
      } else {
        dispatch(setTagsData({ ...tagsData, contacts: selectedContacts }));
        setSelectedContacts([]);
        navigation.goBack();
      }
    } catch (err) {
      console.error("Error setting tags data:", err);
    }
  };

  // Sort and Filter Handlers
  const handleSortIconPress = () => {
    setSortBoxVisible((prev) => !prev);
    setFilterBoxVisible(false);
  };
  const handleFilterIconPress = () => {
    setFilterBoxVisible((prev) => !prev);
    setSortBoxVisible(false);
  };
  const handleSortOption = (option) => {
    setSortBoxVisible(false);
    setActiveSort(option ? option.value : null);
  };
  const handleFilterOption = (option) => {
    setFilterBoxVisible(false);
    setActiveFilter(option ? option.value : null);
  };

  // Filter contacts by search query and filter/sort
  let filteredContacts = contactsList.filter((contact) =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  // Apply Favourites filter
  if (activeFilter === "favourites") {
    filteredContacts = filteredContacts.filter((c) => c.is_favorite === true);
  }
  // Apply sort
  if (activeSort === "az") {
    filteredContacts = [...filteredContacts].sort((a, b) =>
      a.name.localeCompare(b.name)
    );
  } else if (activeSort === "za") {
    filteredContacts = [...filteredContacts].sort((a, b) =>
      b.name.localeCompare(a.name)
    );
  }

  // Toggle select all contacts
  const handleSelectAll = () => {
    if (isAllSelected) {
      setSelectedContacts([]);
      setIsAllSelected(false);
    } else {
      setSelectedContacts(filteredContacts);
      setIsAllSelected(true);
    }
  };

  // Update isAllSelected when selectedContacts or filteredContacts change
  useEffect(() => {
    setIsAllSelected(
      filteredContacts.length > 0 &&
        filteredContacts.every((contact) =>
          selectedContacts.some((sel) => sel.id === contact.id)
        )
    );
  }, [selectedContacts, filteredContacts]);


  // console.log("🚀 ~ AddContactsScreen ~ filteredContacts:", filteredContacts);
  
  return (
    <View style={styles.container}>
      <Header
        title="Add Contacts"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />
      <AppLoader isLoading={loading || contactsLoading} />
      <View style={styles.searchContainer}>
        <SearchBar
          placeholder="Search here"
          value={searchQuery}
          onChangeText={setSearchQuery}
          rightIcon={icons.filterIcon}
          onPressRightIcon1={handleSortIconPress}
          onPressRightIcon2={handleFilterIconPress}
        />
        {/* Sort and Filter Dropdowns */}
        {isSortBoxVisible && (
          <SortOptionsBox
            options={[
              { label: "A-Z", value: "az" },
              { label: "Z-A", value: "za" },
            ]}
            onSelect={handleSortOption}
            style={[styles.sortBoxOverlay, { right: 60 }]}
            optionStyle={styles.sortBoxOption}
            optionTextStyle={styles.optionText}
            activeValue={activeSort}
            allowDeselect={true}
          />
        )}
        {isFilterBoxVisible && (
          <SortOptionsBox
            options={[{ label: "Favourites", value: "favourites" }]}
            onSelect={handleFilterOption}
            style={[styles.sortBoxOverlay, { right: 20 }]}
            optionStyle={styles.sortBoxOption}
            optionTextStyle={styles.optionText}
            activeValue={activeFilter}
            allowDeselect={true}
          />
        )}
      </View>
      <View style={styles.contactHeader}>
        <MyText style={styles.contactLabel}>Contacts</MyText>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <MyText
            onPress={handleSelectAll}
            p
            regular
            underline
            style={{ marginRight: 10 }}
          >
            {isAllSelected ? "Deselect All" : "Select All"}
          </MyText>
        </View>
      </View>
      <FlatList
        data={filteredContacts}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <ContactCard
            name={item.name}
            phone={item.phone}
            isSelected={selectedContacts.some(
              (contact) => contact.id === item.id
            )}
            mode="select"
            onPress={() => toggleSelect(item)}
            imgUrl={item.imgUrl}
          />
        )}
        contentContainerStyle={styles.list}
        ListEmptyComponent={
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              height: 500,
            }}
          >
            <Text>No Contacts Found</Text>
          </View>
        }
      />
      <PrimaryButton
        title={route?.params?.fromProfile ? "Add to profile" : "Add to Tag"}
        onPress={handleAddToTag}
        style={{ bottom: 30 }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  contactHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: "#f2f2f2",
  },
  contactLabel: {
    fontSize: 16,
    fontWeight: "600",
  },
  selectLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#555",
  },
  list: {
    paddingBottom: 80,
  },
  addButton: {
    position: "absolute",
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: "#2E64FE",
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  selectAllLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2E64FE",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 50,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 120,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
  optionText: {
    alignSelf: "left",
  },
});

export default AddContactsScreen;
