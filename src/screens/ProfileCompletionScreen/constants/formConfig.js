export const MAIN_SECTIONS = [
  {
    id: "personal",
    title: "Personal",
  },
  {
    id: "social",
    title: "Social",
  },
  {
    id: "business",
    title: "Business",
  },
];

export const INITIAL_FORM_STATE = {
  // Personal Details
  firstName: "",
  middleName: "",
  lastName: "",
  nickname: [""],
  profileUrl: "",
  email: "",
  phone: "",
  dateOfBirth: "",
  gender: "",
  nationality: "",
  maritalStatus: "",
  spouseName: "",
  profilePicture: "",
  // secondaryEmail: "",
  // secondaryPhone: "",
  otherAddresses: [
    {
      apartment: "",
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
    },
  ],
  childrenNames: [""],
  hobbies: [""],
  religion: [""],
  nativeLanguage: "",
  otherLanguages: [""],
  notes: [""],
  billingAddresses: [
    {
      apartment: "",
      street: "",
      country: "",
      city: "",
      state: "",
      zipCode: "",
    },
  ],
  // card details, can be multiple
  cardDetails: [
    {
      cardName: "",
      cardNumber: "",
      cardExpiry: "",
      cardCvv: "",
    },
  ],
  // Bank account details, can be multiple
  bankAccountDetails: [
    {
      accountName: "",
      accountNumber: "",
      ifscCode: "",
      bankName: "",
    },
  ],
  // PayPal details, can be multiple
  paypalDetails: [
    {
      paypalEmail: "",
      paypalLink: "",
    },
  ],
  // crypto wallet details, can be multiple
  cryptoWalletDetails: [
    {
      walletAddress: "",
      walletType: "",
    },
  ],

  // Health Insurance Information
  policyNumber: "",
  insuranceProvider: "",
  policyPeriod: "",
  effectiveDate: "",
  expirationDate: "",
  sumInsured: "",

  // Address
  street: "",
  city: "",
  state: "",
  zipCode: "",
  country: "",
  apartment: "",

  // Billing Address
  isSameBillingAddress: false,
  billingApartment: "",
  billingStreet: "",
  billingCity: "",
  billingState: "",
  billingZipCode: "",
  billingCountry: "",

  // Card Details
  cardName: "",
  cardNumber: "",
  cardExpiry: "",
  cardCvv: "",

  // Bank Account Details
  accountName: "",
  accountNumber: "",
  ifscCode: "",
  bankName: "",
  paypalEmail: "",

  // Other Address
  isSameAddress: false,
  otherApartment: "",
  otherStreet: "",
  otherCity: "",
  otherState: "",
  otherZipCode: "",
  otherCountry: "",

  // Emergency Contact
  emergencyContactName: "",
  emergencyContactRelationship: "",
  emergencyEmail: "",
  emergencyPhone: "",
  emergencyAddress: "",
  emergencyHobbies: "",
  emergencyReligion: "",
  emergencyContactMethod: "",
  timezone: "",

  // Social Media
  personalWebsite: [""],
  facebook: [""],
  instagram: [""],
  twitter: [""],
  snapchat: [""],
  whatsapp: [""],
  telegram: [""],
  signal: [""],
  skype: [""],
  youtube: [""],
  twitch: [""],
  tiktok: [""],
  linkedIn: [""],
  iMessage: [""],
  googleChat: [""],
  discord: [""],
  wechat: [""],
  kik: [""],
  line: [""],

  // Business Information
  jobTitle: "",
  company: "",
  department: "",
  workEmail: "",
  workPhone: "",
  workFax: "",
  resume: "",
  businessLinkedIn: "",
  businessTwitter: "",
  workHours: "",

  certifications: [""],
  industry: "",
  professionalNotes: [""],

  // company info multiple
  companyInfo: [
    {
      companyName: "",
      companyWebsite: "",
      companyLogo: "",
      companyPhoneNumbers: [""],
      companyEmails: [""],
      companyFaxNumbers: [""],
      companyAddresses: [
        {
          street: "",
          city: "",
          state: "",
          zipCode: "",
          country: "",
        },
      ],
      compnayBillingAddresses: [
        {
          street: "",
          city: "",
          state: "",
          zipCode: "",
          country: "",
        },
      ],
      cardDetails: [
        {
          cardName: "",
          cardNumber: "",
          cardExpiry: "",
          cardCvv: "",
        },
      ],
      bankAccountInfo: [
        {
          accountName: "",
          accountNumber: "",
          ifscCode: "",
          bankName: "",
        },
      ],
      companySocialMedia: {
        facebook: [""],
        instagram: [""],
        twitter: [""],
        snapchat: [""],
        whatsapp: [""],
        telegram: [""],
        signal: [""],
        skype: [""],
        youtube: [""],
        twitch: [""],
        tiktok: [""],
        linkedIn: [""],
        iMessage: [""],
        googleChat: [""],
        discord: [""],
        wechat: [""],
        kik: [""],
        line: [""],
      },
    },
  ],

  // Business Address
  officeBuilding: "",
  businessStreet: "",
  businessCity: "",
  businessState: "",
  businessZipCode: "",
  businessCountry: "",

  // Business Details
};

export const REQUIRED_FIELDS = [
  "firstName",
  "lastName",
  "email",
  "phone",
  "company",
  "jobTitle",
];
