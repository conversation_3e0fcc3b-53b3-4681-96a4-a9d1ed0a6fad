import React, { useState } from "react";
import { View, TouchableOpacity, Alert, Image } from "react-native";
import PropTypes from "prop-types";
import InputField from "../../../components/InputField";
import PhoneNumberField from "../../../components/PhoneNumberField";
import CollapsibleHeader from "./CollapsibleHeader";
import SelectField from "../../../components/SelectField";
import MyText from "../../../components/MyText";
import { styles } from "../styles";
import { industryOptions } from "../../../utils/industryOptions";
import ImagePicker from "react-native-image-crop-picker";
import colors from "../../../assets/colors";
import icons from "../../../assets/icons";
import commonStyles from "../../../assets/commonStyles";
import DocumentUploader from "../../../components/DocumentUploader";
import PhotoUploader from "../../../components/PhotoUploader";
import AddressSection2 from "./AddressSection2";

const BusinessSection = React.forwardRef(
  ({ form, handleChange, errors, ownProfileData }, ref) => {
    const [isBusinessInfoCollapsed, setIsBusinessInfoCollapsed] =
      useState(false);
    const [isBusinessAddressCollapsed, setIsBusinessAddressCollapsed] =
      useState(true);
    const [isBusinessDetailsCollapsed, setIsBusinessDetailsCollapsed] =
      useState(true);
    const [isAccountDetailsCollapsed, setIsAccountDetailsCollapsed] =
      useState(true);
    const [isBillingAddressCollapsed, setIsBillingAddressCollapsed] =
      useState(true);
    const [isCardDetailsCollapsed, setIsCardDetailsCollapsed] = useState(true);
    const [isMessengerCollapsed, setIsMessengerCollapsed] = useState(true);
    const [isSocialMediaCollapsed, setIsSocialMediaCollapsed] = useState(true);

    // Visibility states for business fields
    const [fieldVisibility, setFieldVisibility] = useState({
      // Business Information
      jobTitle: true,
      company: true,
      department: true,
      industry: true,
      workHours: true,
      companyWebsite: true,
      businessLinkedIn: true,
      businessTwitter: true,
      companyLogo: true,

      // Business Address
      officeBuilding: true,
      businessStreet: true,
      businessCity: true,
      businessState: true,
      businessZipCode: true,
      businessCountry: true,

      // Business Details
      workEmail: true,
      workPhone: true,
      workFax: true,
      resume: true,
      certifications: true,
      professionalNotes: true,

      // Account Details
      accountName: true,
      bankName: true,
      accountNumber: true,
      ifscCode: true,
      paypalEmail: true,

      // Billing Address
      billingApartment: true,
      billingStreet: true,
      billingCity: true,
      billingState: true,
      billingZipCode: true,
      billingCountry: true,

      // Card Details
      cardName: true,
      cardNumber: true,
      cardExpiry: true,
      cardCvv: true,

      // Company Messenger IDs
      iMessage: true,
      googleChat: true,
      discord: true,
      slack: true,
      wechat: true,
      kik: true,
      line: true,

      // Company Social Media
      facebook: true,
      instagram: true,
      twitter: true,
      linkedIn: true,
      snapchat: true,
      whatsapp: true,
      telegram: true,
      signal: true,
      skype: true,
      youtube: true,
      twitch: true,
      tiktok: true,
    });

    // Handle visibility toggle for a field
    const handleVisibilityToggle = (fieldName) => {
      setFieldVisibility((prev) => ({
        ...prev,
        [fieldName]: !prev[fieldName],
      }));
    };

    // Expose methods through the ref
    React.useImperativeHandle(ref, () => ({
      getFieldVisibility: () => fieldVisibility,
    }));

    // const handleImagePicker = (field) => {
    //   Alert.alert(
    //     "Upload Image",
    //     "Choose a method",
    //     [
    //       { text: "Camera", onPress: () => openCamera(field) },
    //       { text: "Gallery", onPress: () => openGallery(field) },
    //       { text: "Cancel", style: "cancel" },
    //     ],
    //     { cancelable: true }
    //   );
    // };

    // const openCamera = async (field) => {
    //   try {
    //     const image = await ImagePicker.openCamera({
    //       cropping: true,
    //       compressImageQuality: 0.8,
    //     });
    //     handleChange(field, image.path);
    //   } catch (error) {
    //     console.log("Camera cancelled or failed:", error?.message);
    //   }
    // };

    // const openGallery = async (field) => {
    //   try {
    //     const image = await ImagePicker.openPicker({
    //       cropping: true,
    //       compressImageQuality: 0.8,
    //     });
    //     handleChange(field, image.path);
    //   } catch (error) {
    //     console.log("Gallery cancelled or failed:", error?.message);
    //   }
    // };

    return (
      <View>
        <CollapsibleHeader
          title="Employee Information"
          isCollapsed={isBusinessInfoCollapsed}
          onToggle={() => setIsBusinessInfoCollapsed(!isBusinessInfoCollapsed)}
        />
        {!isBusinessInfoCollapsed && (
          <View style={{ zIndex: 3, position: "relative" }}>
            {/* <DocumentUploader onUploadSuccess={(doc) => console.log("Document uploaded successfully", doc)} /> */}
            <InputField
              label="Job Title*"
              value={form.jobTitle}
              onChangeText={(val) => handleChange("jobTitle", val)}
              error={errors.jobTitle}
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.jobTitle}
              onVisibilityToggle={() => handleVisibilityToggle("jobTitle")}
            />
            <InputField
              label="Company Name*"
              value={form.company}
              onChangeText={(val) => handleChange("company", val)}
              error={errors.company}
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.company}
              onVisibilityToggle={() => handleVisibilityToggle("company")}
            />
            <InputField
              label="Department"
              value={form.department}
              onChangeText={(val) => handleChange("department", val)}
              error={errors.department}
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.department}
              onVisibilityToggle={() => handleVisibilityToggle("department")}
            />

            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <MyText p medium style={styles.label}>
                Work Phone
              </MyText>
            </View>
            <PhoneNumberField
              value={form.workPhone}
              onChangeRaw={(val) => handleChange("workPhone", val)}
              setCountryCode={() => {}}
              error={errors.workPhone}
              setError={() => {}}
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.workPhone}
              onVisibilityToggle={() => handleVisibilityToggle("workPhone")}
            />
            <InputField
              label="Work Email"
              value={form.workEmail}
              onChangeText={(val) => handleChange("workEmail", val)}
              error={errors.workEmail}
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.workEmail}
              onVisibilityToggle={() => handleVisibilityToggle("workEmail")}
            />

            <InputField
              label="Work Fax Number"
              value={form.workFax}
              onChangeText={(val) => handleChange("workFax", val)}
              error={errors.workFax}
              keyboardType="phone-pad"
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.workFax}
              onVisibilityToggle={() => handleVisibilityToggle("workFax")}
            />

            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <MyText p medium style={styles.label}>
                Resume
              </MyText>
              {!ownProfileData && (
                <TouchableOpacity
                  style={{ padding: 5 }}
                  onPress={() => handleVisibilityToggle("resume")}
                >
                  <Image
                    source={
                      fieldVisibility.resume
                        ? icons.openEyeProfileIcon
                        : icons.closedEyeProfileIcon
                    }
                    style={{ width: 20, height: 20 }}
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              )}
            </View>
            {/* <TouchableOpacity
              style={{
                backgroundColor: colors.inputBg,
                borderRadius: 10,
                padding: 12,
                marginBottom: 16,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
              onPress={() => handleImagePicker("resume")}
            >
              <MyText p style={{ color: form.resume ? colors.black : "#888" }}>
                {form.resume ? "Resume Selected" : "Upload Resume"}
              </MyText>
              <Image
                source={icons.rightArrowIcon}
                style={commonStyles.extraSmallIcon}
                resizeMode="contain"
              />
            </TouchableOpacity> */}
            <DocumentUploader
              onUploadSuccess={(doc) => handleChange("resume", doc)}
              label="Upload Resume"
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.resume}
              onVisibilityToggle={() => handleVisibilityToggle("resume")}
            />

            <InputField
              label="LinkedIn Profile"
              value={form.businessLinkedIn}
              onChangeText={(val) => handleChange("businessLinkedIn", val)}
              error={errors.businessLinkedIn}
              placeholder="https://linkedin.com/in/username"
              keyboardType="url"
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.businessLinkedIn}
              onVisibilityToggle={() =>
                handleVisibilityToggle("businessLinkedIn")
              }
            />
            <InputField
              label="X Profile"
              value={form.businessTwitter}
              onChangeText={(val) => handleChange("businessTwitter", val)}
              error={errors.businessTwitter}
              placeholder="https://twitter.com/username"
              keyboardType="url"
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.businessTwitter}
              onVisibilityToggle={() =>
                handleVisibilityToggle("businessTwitter")
              }
            />
            <InputField
              label="Work Hours"
              value={form.workHours}
              onChangeText={(val) => handleChange("workHours", val)}
              error={errors.workHours}
              placeholder="e.g., 9:00 AM - 5:00 PM, Monday-Friday"
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.workHours}
              onVisibilityToggle={() => handleVisibilityToggle("workHours")}
            />

            {/* <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <MyText p medium style={styles.label}>
                Professional Certifications
              </MyText>
              <TouchableOpacity
                style={{ padding: 5 }}
                onPress={() => handleVisibilityToggle("certifications")}
              >
                <Image
                  source={
                    fieldVisibility.certifications
                      ? icons.openEyeProfileIcon
                      : icons.closedEyeProfileIcon
                  }
                  style={{ width: 20, height: 20 }}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
            <DocumentUploader
              onUploadSuccess={(doc) =>
                handleChange("certifications", doc)
              }
              label="Upload Certifications"
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.certifications}
              onVisibilityToggle={() =>
                handleVisibilityToggle("certifications")
              }
            /> */}

            {form.certifications &&
              form.certifications.length > 0 &&
              form.certifications.map((cert, index) => (
                <View key={index} style={{ marginVertical: 16 }}>
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <MyText p medium style={styles.label}>
                      {form.certifications.length > 1
                        ? `Professional Certification ${index + 1}`
                        : "Professional Certifications"}
                    </MyText>
                    {!ownProfileData && (
                      <TouchableOpacity
                        style={{ padding: 5 }}
                        onPress={() =>
                          handleVisibilityToggle(`certifications-${index}`)
                        }
                      >
                        <Image
                          source={
                            fieldVisibility[`certifications-${index}`]
                              ? icons.openEyeProfileIcon
                              : icons.closedEyeProfileIcon
                          }
                          style={{ width: 20, height: 20 }}
                          resizeMode="contain"
                        />
                      </TouchableOpacity>
                    )}
                    {/* Remove button left of eye button */}
                    {form.certifications.length > 1 && (
                      <TouchableOpacity
                        onPress={() => {
                          const updatedCertifications = [
                            ...form.certifications,
                          ];
                          updatedCertifications.splice(index, 1);
                          handleChange("certifications", updatedCertifications);
                        }}
                        style={{
                          position: "absolute",
                          right: 40,
                          top: -5,
                          zIndex: 1,
                          padding: 10,
                        }}
                      >
                        <Image
                          source={icons.minusIcon}
                          resizeMode="contain"
                          style={{ width: 20, height: 20 }}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                  <DocumentUploader
                    onUploadSuccess={(doc) => {
                      const updatedCertifications = [...form.certifications];
                      updatedCertifications[index] = doc;
                      handleChange("certifications", updatedCertifications);
                    }}
                    label={`Upload Certification ${index + 1}`}
                    showVisibilityToggle={!ownProfileData}
                    isVisible={fieldVisibility[`certifications-${index}`]}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle(`certifications-${index}`)
                    }
                  />
                </View>
              ))}
            {/* add mroe button */}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => {
                handleChange("certifications", [
                  ...(form.certifications || []),
                  "",
                ]);
              }}
            >
              +Add More Certifications
            </MyText>

            <SelectField
              label="Industry"
              data={industryOptions}
              defaultValue={form.industry}
              onSelect={(item) => handleChange("industry", item.value)}
              backgroundColor="#f2f2f2"
              height={48}
              width="100%"
              showIcon={true}
              error={errors.industry}
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.industry}
              onVisibilityToggle={() => handleVisibilityToggle("industry")}
            />
            {/* <InputField
              label="Professional Notes not covered above"
              value={form.professionalNotes}
              onChangeText={(val) => handleChange("professionalNotes", val)}
              error={errors.professionalNotes}
              placeholder="Add any additional professional information here"
              multiline={true}
              numberOfLines={4}
              textAlignVertical="top"
              style={{ height: 100, textAlignVertical: "top", paddingTop: 10 }}
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility.professionalNotes}
              onVisibilityToggle={() =>
                handleVisibilityToggle("professionalNotes")
              }
            /> */}

            {/* professional notes can be multiple */}
            {form.professionalNotes &&
              form.professionalNotes.length > 0 &&
              form.professionalNotes.map((note, index) => (
                <View key={index} style={{ marginVertical: 16 }}>
                  <InputField
                    label={
                      form.professionalNotes.length > 1
                        ? `Professional Note ${index + 1}`
                        : "Professional Notes"
                    }
                    value={note}
                    onChangeText={(val) => {
                      const updatedNotes = [...form.professionalNotes];
                      updatedNotes[index] = val;
                      handleChange("professionalNotes", updatedNotes);
                    }}
                    error={errors.professionalNotes}
                    placeholder="Add any additional professional information here"
                    multiline={true}
                    numberOfLines={4}
                    textAlignVertical="top"
                    style={{
                      height: 100,
                      textAlignVertical: "top",
                      paddingTop: 10,
                    }}
                    showVisibilityToggle={!ownProfileData}
                    isVisible={fieldVisibility.professionalNotes}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle("professionalNotes")
                    }
                  />
                  {/* <View style={{ flexDirection: "row", justifyContent: "flex-end" }}> */}
                  {form.professionalNotes.length > 1 && (
                    <TouchableOpacity
                      onPress={() => {
                        const updatedNotes = [...form.professionalNotes];
                        updatedNotes.splice(index, 1);
                        handleChange("professionalNotes", updatedNotes);
                      }}
                      style={{
                        position: "absolute",
                        right: 5,
                        top: -15,
                        zIndex: 1,
                        padding: 10,
                      }}
                    >
                      <Image
                        source={icons.minusIcon}
                        resizeMode="contain"
                        style={{ width: 20, height: 20 }}
                      />
                    </TouchableOpacity>
                  )}
                  {/* </View> */}
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => {
                handleChange("professionalNotes", [
                  ...(form.professionalNotes || []),
                  "",
                ]);
              }}
            >
              +Add More Professional Notes
            </MyText>
          </View>
        )}

        {form.companyInfo &&
          form.companyInfo.length > 0 &&
          form.companyInfo.map((company, index) => (
            <View key={index} style={{ marginVertical: 16 }}>
              <CollapsibleHeader
                title="Company Information"
                isCollapsed={isBusinessDetailsCollapsed}
                onToggle={() =>
                  setIsBusinessDetailsCollapsed(!isBusinessDetailsCollapsed)
                }
              />
              {!isBusinessDetailsCollapsed && (
                <View style={{ zIndex: 1, position: "relative" }}>
                  {/* companyInfo: [{
    companyName: "",
    companyWebsite: "",
    companyLogo: "",
  }], */}
                  {/* company Name*/}
                  <InputField
                    label="Company Name"
                    value={company.companyName}
                    onChangeText={(val) => {
                      const updatedCompanies = [...form.companyInfo];
                      updatedCompanies[index] = {
                        ...updatedCompanies[index],
                        companyName: val,
                      };
                      handleChange("companyInfo", updatedCompanies);
                    }}
                    error={errors.companyName}
                    showVisibilityToggle={!ownProfileData}
                    isVisible={fieldVisibility.companyName}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle("companyName")
                    }
                  />

                  <MyText p medium style={styles.label}>
                    Company Logo
                  </MyText>
                  <PhotoUploader
                    onUploadSuccess={(image) => {
                      console.log("Company Logo uploaded successfully", image);
                      const updatedCompanies = [...form.companyInfo];
                      updatedCompanies[index] = {
                        ...updatedCompanies[index],
                        companyLogo: image,
                      };
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  />

                  <InputField
                    label="Company Website"
                    value={company.companyWebsite}
                    onChangeText={(val) => {
                      const updatedCompanies = [...form.companyInfo];
                      updatedCompanies[index] = {
                        ...updatedCompanies[index],
                        companyWebsite: val,
                      };
                      handleChange("companyInfo", updatedCompanies);
                    }}
                    error={errors.companyWebsite}
                    placeholder="https://company.com"
                    keyboardType="url"
                    showVisibilityToggle={!ownProfileData}
                    isVisible={fieldVisibility.companyWebsite}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle("companyWebsite")
                    }
                  />

                  {/* company phone number, can be multiple */}
                  {company.companyPhoneNumbers &&
                    company.companyPhoneNumbers.length > 0 &&
                    company.companyPhoneNumbers.map((phone, phoneIndex) => (
                      <View key={phoneIndex} style={{ marginVertical: 8 }}>
                        <MyText p medium style={styles.label}>
                          {company.companyPhoneNumbers.length > 1
                            ? `Company Phone Number ${phoneIndex + 1}`
                            : "Company Phone Numbers"}
                        </MyText>
                        <PhoneNumberField
                          value={phone}
                          onChangeRaw={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].companyPhoneNumbers[
                              phoneIndex
                            ] = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          setCountryCode={() => {}}
                          error={errors.companyPhoneNumbers}
                          setError={() => {}}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.companyPhoneNumbers}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("companyPhoneNumbers")
                          }
                        />
                        {company.companyPhoneNumbers.length > 1 && (
                          <TouchableOpacity
                            onPress={() => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companyPhoneNumbers.splice(phoneIndex, 1);
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            style={{
                              position: "absolute",
                              right: 5,
                              top: -15,
                              zIndex: 1,
                              padding: 10,
                            }}
                          >
                            <Image
                              source={icons.minusIcon}
                              resizeMode="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companyPhoneNumbers) {
                        updatedCompanies[index].companyPhoneNumbers = [];
                      }
                      updatedCompanies[index].companyPhoneNumbers.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Company Phone Numbers
                  </MyText>

                  {/* company emails, can be multiple */}
                  {company.companyEmails &&
                    company.companyEmails.length > 0 &&
                    company.companyEmails.map((email, emailIndex) => (
                      <View key={emailIndex} style={{ marginVertical: 8 }}>
                        <MyText p medium style={styles.label}>
                          {company.companyEmails.length > 1
                            ? `Company Email ${emailIndex + 1}`
                            : "Company Emails"}
                        </MyText>
                        <InputField
                          label="Email"
                          value={email}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].companyEmails[emailIndex] =
                              val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.companyEmails}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.companyEmails}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("companyEmails")
                          }
                        />
                        {company.companyEmails.length > 1 && (
                          <TouchableOpacity
                            onPress={() => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companyEmails.splice(
                                emailIndex,
                                1
                              );
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            style={{
                              position: "absolute",
                              right: 5,
                              top: -15,
                              zIndex: 1,
                              padding: 10,
                            }}
                          >
                            <Image
                              source={icons.minusIcon}
                              resizeMode="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companyEmails) {
                        updatedCompanies[index].companyEmails = [];
                      }
                      updatedCompanies[index].companyEmails.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Company Emails
                  </MyText>

                  {/* company fax numbers, can be multiple */}
                  {company.companyFaxNumbers &&
                    company.companyFaxNumbers.length > 0 &&
                    company.companyFaxNumbers.map((fax, faxIndex) => (
                      <View key={faxIndex} style={{ marginVertical: 8 }}>
                        <MyText p medium style={styles.label}>
                          {company.companyFaxNumbers.length > 1
                            ? `Company Fax Number ${faxIndex + 1}`
                            : "Company Fax Numbers"}
                        </MyText>
                        <InputField
                          label="Fax Number"
                          value={fax}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].companyFaxNumbers[
                              faxIndex
                            ] = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.companyFaxNumbers}
                          keyboardType="phone-pad"
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.companyFaxNumbers}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("companyFaxNumbers")
                          }
                        />
                        {company.companyFaxNumbers.length > 1 && (
                          <TouchableOpacity
                            onPress={() => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companyFaxNumbers.splice(
                                faxIndex,
                                1
                              );
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            style={{
                              position: "absolute",
                              right: 5,
                              top: -15,
                              zIndex: 1,
                              padding: 10,
                            }}
                          >
                            <Image
                              source={icons.minusIcon}
                              resizeMode="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companyFaxNumbers) {
                        updatedCompanies[index].companyFaxNumbers = [];
                      }
                      updatedCompanies[index].companyFaxNumbers.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Company Fax Numbers
                  </MyText>

                  {/* company adresses, can be multiple */}
                  {company.companyAddresses &&
                    company.companyAddresses.length > 0 &&
                    company.companyAddresses.map((address, addressIndex) => (
                      <View key={addressIndex} style={{ marginVertical: 8 }}>
                        <MyText p medium style={styles.label}>
                          {company.companyAddresses.length > 1
                            ? `Company Address ${addressIndex + 1}`
                            : "Company Addresses"}
                        </MyText>
                        <AddressSection2
                          form={address}
                          handleChange={(field, value) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].companyAddresses[
                              addressIndex
                            ] = {
                              ...updatedCompanies[index].companyAddresses[
                                addressIndex
                              ],
                              ...value,
                            };
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          errors={errors}
                          address={address}
                          addressIndex={addressIndex}
                          fieldVisibility={fieldVisibility}
                          handleVisibilityToggle={handleVisibilityToggle}
                          // prefix={`companyInfo[${index}].companyAddresses[${addressIndex}].`}
                          disabled={false}
                          sourceForm={company.companyAddresses[addressIndex]}
                          noPrefix={true}
                          showVisibilityToggle={!ownProfileData}
                        />
                        {company.companyAddresses.length > 1 && (
                          <TouchableOpacity
                            onPress={() => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companyAddresses.splice(
                                addressIndex,
                                1
                              );
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            style={{
                              position: "absolute",
                              right: 5,
                              top: -15,
                              zIndex: 1,
                              padding: 10,
                            }}
                          >
                            <Image
                              source={icons.minusIcon}
                              resizeMode="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companyAddresses) {
                        updatedCompanies[index].companyAddresses = [];
                      }
                      updatedCompanies[index].companyAddresses.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Company Addresses
                  </MyText>

                  {/* company billing addresses, can be multiple */}
                  {company.companyBillingAddresses &&
                    company.companyBillingAddresses.length > 0 &&
                    company.companyBillingAddresses.map(
                      (billingAddress, billingIndex) => (
                        <View key={billingIndex} style={{ marginVertical: 8 }}>
                          <MyText p medium style={styles.label}>
                            {company.companyBillingAddresses.length > 1
                              ? `Company Billing Address ${billingIndex + 1}`
                              : "Company Billing Addresses"}
                          </MyText>
                          <AddressSection2
                            form={billingAddress}
                            handleChange={(field, value) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companyBillingAddresses[
                                billingIndex
                              ] = {
                                ...updatedCompanies[index]
                                  .companyBillingAddresses[billingIndex],
                                ...value,
                              };
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            errors={errors}
                            address={billingAddress}
                            addressIndex={billingIndex}
                            fieldVisibility={fieldVisibility}
                            handleVisibilityToggle={handleVisibilityToggle}
                            // prefix={`companyInfo[${index}].companyBillingAddresses[${billingIndex}].`}
                            disabled={false}
                            sourceForm={
                              company.companyBillingAddresses[billingIndex]
                            }
                            noPrefix={true}
                          />
                          {company.companyBillingAddresses.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companyBillingAddresses.splice(
                                  billingIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companyBillingAddresses) {
                        updatedCompanies[index].companyBillingAddresses = [];
                      }
                      updatedCompanies[index].companyBillingAddresses.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Company Billing Addresses
                  </MyText>

                  {/* card information, can be multiple */}
                  {company.cardDetails &&
                    company.cardDetails.length > 0 &&
                    company.cardDetails.map((card, cardIndex) => (
                      <View key={cardIndex} style={{ marginVertical: 8 }}>
                        <MyText p medium style={styles.label}>
                          {company.cardDetails.length > 1
                            ? `Card Details ${cardIndex + 1}`
                            : "Card Details"}
                        </MyText>
                        <InputField
                          label="Card Name"
                          value={card.cardName}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].cardDetails[
                              cardIndex
                            ].cardName = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.cardName}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.cardName}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("cardName")
                          }
                        />
                        <InputField
                          label="Card Number"
                          value={card.cardNumber}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].cardDetails[
                              cardIndex
                            ].cardNumber = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.cardNumber}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.cardNumber}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("cardNumber")
                          }
                        />
                        <InputField
                          label="Card Expiry"
                          value={card.cardExpiry}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].cardDetails[
                              cardIndex
                            ].cardExpiry = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.cardExpiry}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.cardExpiry}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("cardExpiry")
                          }
                        />
                        <InputField
                          label="Card CVV"
                          value={card.cardCvv}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].cardDetails[
                              cardIndex
                            ].cardCvv = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.cardCvv}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.cardCvv}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("cardCvv")
                          }
                        />
                        {company.cardDetails.length > 1 && (
                          <TouchableOpacity
                            onPress={() => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].cardDetails.splice(
                                cardIndex,
                                1
                              );
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            style={{
                              position: "absolute",
                              right: 5,
                              top: -15,
                              zIndex: 1,
                              padding: 10,
                            }}
                          >
                            <Image
                              source={icons.minusIcon}
                              resizeMode="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].cardDetails) {
                        updatedCompanies[index].cardDetails = [];
                      }
                      updatedCompanies[index].cardDetails.push({
                        cardName: "",
                        cardNumber: "",
                        cardExpiry: "",
                        cardCvv: "",
                      });
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Card Details
                  </MyText>

                  {/* bankAccountInfo, can be multiple */}
                  {company.bankAccountInfo &&
                    company.bankAccountInfo.length > 0 &&
                    company.bankAccountInfo.map((bank, bankIndex) => (
                      <View key={bankIndex} style={{ marginVertical: 8 }}>
                        <MyText p medium style={styles.label}>
                          {company.bankAccountInfo.length > 1
                            ? `Bank Account Info ${bankIndex + 1}`
                            : "Bank Account Info"}
                        </MyText>
                        <InputField
                          label="Account Name"
                          value={bank.accountName}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].bankAccountInfo[
                              bankIndex
                            ].accountName = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.accountName}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.accountName}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("accountName")
                          }
                        />
                        <InputField
                          label="Bank Name"
                          value={bank.bankName}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].bankAccountInfo[
                              bankIndex
                            ].bankName = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.bankName}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.bankName}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("bankName")
                          }
                        />
                        <InputField
                          label="Account Number"
                          value={bank.accountNumber}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].bankAccountInfo[
                              bankIndex
                            ].accountNumber = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.accountNumber}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.accountNumber}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("accountNumber")
                          }
                        />
                        <InputField
                          label="IFSC Code"
                          value={bank.ifscCode}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].bankAccountInfo[
                              bankIndex
                            ].ifscCode = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.ifscCode}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.ifscCode}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("ifscCode")
                          }
                        />
                        <InputField
                          label="PayPal Email"
                          value={bank.paypalEmail}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].bankAccountInfo[
                              bankIndex
                            ].paypalEmail = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.paypalEmail}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.paypalEmail}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("paypalEmail")
                          }
                        />
                        {company.bankAccountInfo.length > 1 && (
                          <TouchableOpacity
                            onPress={() => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].bankAccountInfo.splice(
                                bankIndex,
                                1
                              );
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            style={{
                              position: "absolute",
                              right: 5,
                              top: -15,
                              zIndex: 1,
                              padding: 10,
                            }}
                          >
                            <Image
                              source={icons.minusIcon}
                              resizeMode="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].bankAccountInfo) {
                        updatedCompanies[index].bankAccountInfo = [];
                      }
                      updatedCompanies[index].bankAccountInfo.push({
                        accountName: "",
                        bankName: "",
                        accountNumber: "",
                        ifscCode: "",
                        paypalEmail: "",
                      });
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Bank Account Info
                  </MyText>

                  <MyText style={styles.label} p medium>
                    Company Social Media Accounts
                  </MyText>

                  {/* facebook can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.facebook &&
                    company.companySocialMedia.facebook.length > 0 &&
                    company.companySocialMedia.facebook.map(
                      (facebook, facebookIndex) => (
                        <View key={facebookIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={facebookIndex}
                            label={
                              company.companySocialMedia.facebook.length > 0
                                ? `Facebook ${facebookIndex + 1}`
                                : "Facebook"
                            }
                            value={facebook}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.facebook[facebookIndex] =
                                val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.facebook}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.facebook}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("facebook")
                            }
                          />
                          {company.companySocialMedia.facebook.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.facebook.splice(
                                  facebookIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (
                        !updatedCompanies[index].companySocialMedia.facebook
                      ) {
                        updatedCompanies[index].companySocialMedia.facebook =
                          [];
                      }
                      updatedCompanies[index].companySocialMedia.facebook.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Facebook Accounts
                  </MyText>

                  {/* instagram, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.instagram &&
                    company.companySocialMedia.instagram.length > 0 &&
                    company.companySocialMedia.instagram.map(
                      (instagram, instagramIndex) => (
                        <View
                          key={instagramIndex}
                          style={{ marginVertical: 8 }}
                        >
                          <InputField
                            key={instagramIndex}
                            label={
                              company.companySocialMedia.instagram.length > 0
                                ? `Instagram ${instagramIndex + 1}`
                                : "Instagram"
                            }
                            value={instagram}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.instagram[instagramIndex] =
                                val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.instagram}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.instagram}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("instagram")
                            }
                          />
                          {company.companySocialMedia.instagram.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.instagram.splice(
                                  instagramIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (
                        !updatedCompanies[index].companySocialMedia.instagram
                      ) {
                        updatedCompanies[index].companySocialMedia.instagram =
                          [];
                      }
                      updatedCompanies[index].companySocialMedia.instagram.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Instagram Accounts
                  </MyText>

                  {/* twitter, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.twitter &&
                    company.companySocialMedia.twitter.length > 0 &&
                    company.companySocialMedia.twitter.map(
                      (twitter, twitterIndex) => (
                        <View key={twitterIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={twitterIndex}
                            label={
                              company.companySocialMedia.twitter.length > 0
                                ? `Twitter ${twitterIndex + 1}`
                                : "Twitter"
                            }
                            value={twitter}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.twitter[twitterIndex] = val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.twitter}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.twitter}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("twitter")
                            }
                          />
                          {company.companySocialMedia.twitter.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.twitter.splice(
                                  twitterIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.twitter) {
                        updatedCompanies[index].companySocialMedia.twitter = [];
                      }
                      updatedCompanies[index].companySocialMedia.twitter.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Twitter Accounts
                  </MyText>

                  {/* linkedIn, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.linkedIn &&
                    company.companySocialMedia.linkedIn.length > 0 &&
                    company.companySocialMedia.linkedIn.map(
                      (linkedIn, linkedInIndex) => (
                        <View key={linkedInIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={linkedInIndex}
                            label={
                              company.companySocialMedia.linkedIn.length > 0
                                ? `LinkedIn ${linkedInIndex + 1}`
                                : "LinkedIn"
                            }
                            value={linkedIn}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.linkedIn[linkedInIndex] =
                                val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.linkedIn}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.linkedIn}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("linkedIn")
                            }
                          />
                          {company.companySocialMedia.linkedIn.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.linkedIn.splice(
                                  linkedInIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (
                        !updatedCompanies[index].companySocialMedia.linkedIn
                      ) {
                        updatedCompanies[index].companySocialMedia.linkedIn =
                          [];
                      }
                      updatedCompanies[index].companySocialMedia.linkedIn.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More LinkedIn Accounts
                  </MyText>

                  {/* snapchat, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.snapchat &&
                    company.companySocialMedia.snapchat.length > 0 &&
                    company.companySocialMedia.snapchat.map(
                      (snapchat, snapchatIndex) => (
                        <View key={snapchatIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={snapchatIndex}
                            label={
                              company.companySocialMedia.snapchat.length > 0
                                ? `Snapchat ${snapchatIndex + 1}`
                                : "Snapchat"
                            }
                            value={snapchat}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.snapchat[snapchatIndex] =
                                val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.snapchat}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.snapchat}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("snapchat")
                            }
                          />
                          {company.companySocialMedia.snapchat.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.snapchat.splice(
                                  snapchatIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (
                        !updatedCompanies[index].companySocialMedia.snapchat
                      ) {
                        updatedCompanies[index].companySocialMedia.snapchat =
                          [];
                      }
                      updatedCompanies[index].companySocialMedia.snapchat.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Snapchat Accounts
                  </MyText>

                  {/* whatsapp, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.whatsapp &&
                    company.companySocialMedia.whatsapp.length > 0 &&
                    company.companySocialMedia.whatsapp.map(
                      (whatsapp, whatsappIndex) => (
                        <View key={whatsappIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={whatsappIndex}
                            label={
                              company.companySocialMedia.whatsapp.length > 0
                                ? `WhatsApp ${whatsappIndex + 1}`
                                : "WhatsApp"
                            }
                            value={whatsapp}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.whatsapp[whatsappIndex] =
                                val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.whatsapp}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.whatsapp}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("whatsapp")
                            }
                          />
                          {company.companySocialMedia.whatsapp.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.whatsapp.splice(
                                  whatsappIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (
                        !updatedCompanies[index].companySocialMedia.whatsapp
                      ) {
                        updatedCompanies[index].companySocialMedia.whatsapp =
                          [];
                      }
                      updatedCompanies[index].companySocialMedia.whatsapp.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More WhatsApp Accounts
                  </MyText>

                  {/* telegram, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.telegram &&
                    company.companySocialMedia.telegram.length > 0 &&
                    company.companySocialMedia.telegram.map(
                      (telegram, telegramIndex) => (
                        <View key={telegramIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={telegramIndex}
                            label={
                              company.companySocialMedia.telegram.length > 0
                                ? `Telegram ${telegramIndex + 1}`
                                : "Telegram"
                            }
                            value={telegram}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.telegram[telegramIndex] =
                                val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.telegram}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.telegram}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("telegram")
                            }
                          />
                          {company.companySocialMedia.telegram.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.telegram.splice(
                                  telegramIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (
                        !updatedCompanies[index].companySocialMedia.telegram
                      ) {
                        updatedCompanies[index].companySocialMedia.telegram =
                          [];
                      }
                      updatedCompanies[index].companySocialMedia.telegram.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Telegram Accounts
                  </MyText>

                  {/* signal, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.signal &&
                    company.companySocialMedia.signal.length > 0 &&
                    company.companySocialMedia.signal.map(
                      (signal, signalIndex) => (
                        <View key={signalIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={signalIndex}
                            label={
                              company.companySocialMedia.signal.length > 0
                                ? `Signal ${signalIndex + 1}`
                                : "Signal"
                            }
                            value={signal}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companySocialMedia.signal[
                                signalIndex
                              ] = val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.signal}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.signal}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("signal")
                            }
                          />
                          {company.companySocialMedia.signal.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.signal.splice(
                                  signalIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.signal) {
                        updatedCompanies[index].companySocialMedia.signal = [];
                      }
                      updatedCompanies[index].companySocialMedia.signal.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Signal Accounts
                  </MyText>

                  {/* skype, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.skype &&
                    company.companySocialMedia.skype.length > 0 &&
                    company.companySocialMedia.skype.map(
                      (skype, skypeIndex) => (
                        <View key={skypeIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={skypeIndex}
                            label={
                              company.companySocialMedia.skype.length > 0
                                ? `Skype ${skypeIndex + 1}`
                                : "Skype"
                            }
                            value={skype}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companySocialMedia.skype[
                                skypeIndex
                              ] = val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.skype}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.skype}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("skype")
                            }
                          />
                          {company.companySocialMedia.skype.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.skype.splice(
                                  skypeIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.skype) {
                        updatedCompanies[index].companySocialMedia.skype = [];
                      }
                      updatedCompanies[index].companySocialMedia.skype.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Skype Accounts
                  </MyText>

                  {/* youtube, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.youtube &&
                    company.companySocialMedia.youtube.length > 0 &&
                    company.companySocialMedia.youtube.map(
                      (youtube, youtubeIndex) => (
                        <View key={youtubeIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={youtubeIndex}
                            label={
                              company.companySocialMedia.youtube.length > 0
                                ? `YouTube ${youtubeIndex + 1}`
                                : "YouTube"
                            }
                            value={youtube}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.youtube[youtubeIndex] = val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.youtube}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.youtube}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("youtube")
                            }
                          />
                          {company.companySocialMedia.youtube.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.youtube.splice(
                                  youtubeIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.youtube) {
                        updatedCompanies[index].companySocialMedia.youtube = [];
                      }
                      updatedCompanies[index].companySocialMedia.youtube.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More YouTube Accounts
                  </MyText>

                  {/* twitch, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.twitch &&
                    company.companySocialMedia.twitch.length > 0 &&
                    company.companySocialMedia.twitch.map(
                      (twitch, twitchIndex) => (
                        <View key={twitchIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={twitchIndex}
                            label={
                              company.companySocialMedia.twitch.length > 0
                                ? `Twitch ${twitchIndex + 1}`
                                : "Twitch"
                            }
                            value={twitch}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companySocialMedia.twitch[
                                twitchIndex
                              ] = val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.twitch}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.twitch}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("twitch")
                            }
                          />
                          {company.companySocialMedia.twitch.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.twitch.splice(
                                  twitchIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.twitch) {
                        updatedCompanies[index].companySocialMedia.twitch = [];
                      }
                      updatedCompanies[index].companySocialMedia.twitch.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Twitch Accounts
                  </MyText>

                  {/* tiktok, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.tiktok &&
                    company.companySocialMedia.tiktok.length > 0 &&
                    company.companySocialMedia.tiktok.map(
                      (tiktok, tiktokIndex) => (
                        <View key={tiktokIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={tiktokIndex}
                            label={
                              company.companySocialMedia.tiktok.length > 0
                                ? `TikTok ${tiktokIndex + 1}`
                                : "TikTok"
                            }
                            value={tiktok}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companySocialMedia.tiktok[
                                tiktokIndex
                              ] = val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.tiktok}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.tiktok}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("tiktok")
                            }
                          />
                          {company.companySocialMedia.tiktok.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.tiktok.splice(
                                  tiktokIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.tiktok) {
                        updatedCompanies[index].companySocialMedia.tiktok = [];
                      }
                      updatedCompanies[index].companySocialMedia.tiktok.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More TikTok Accounts
                  </MyText>

                  {/* iMessage, can be multiple */}

                  {company.companySocialMedia &&
                    company.companySocialMedia.iMessage &&
                    company.companySocialMedia.iMessage.length > 0 &&
                    company.companySocialMedia.iMessage.map(
                      (iMessage, iMessageIndex) => (
                        <View key={iMessageIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={iMessageIndex}
                            label={
                              company.companySocialMedia.iMessage.length > 0
                                ? `iMessage ${iMessageIndex + 1}`
                                : "iMessage"
                            }
                            value={iMessage}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.iMessage[iMessageIndex] =
                                val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.iMessage}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.iMessage}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("iMessage")
                            }
                          />
                          {company.companySocialMedia.iMessage.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.iMessage.splice(
                                  iMessageIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (
                        !updatedCompanies[index].companySocialMedia.iMessage
                      ) {
                        updatedCompanies[index].companySocialMedia.iMessage =
                          [];
                      }
                      updatedCompanies[index].companySocialMedia.iMessage.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More iMessage Accounts
                  </MyText>

                  {/* googleChat, can be multiple */}

                  {company.companySocialMedia &&
                    company.companySocialMedia.googleChat &&
                    company.companySocialMedia.googleChat.length > 0 &&
                    company.companySocialMedia.googleChat.map(
                      (googleChat, googleChatIndex) => (
                        <View
                          key={googleChatIndex}
                          style={{ marginVertical: 8 }}
                        >
                          <InputField
                            key={googleChatIndex}
                            label={
                              company.companySocialMedia.googleChat.length > 0
                                ? `Google Chat ${googleChatIndex + 1}`
                                : "Google Chat"
                            }
                            value={googleChat}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.googleChat[googleChatIndex] =
                                val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.googleChat}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.googleChat}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("googleChat")
                            }
                          />
                          {company.companySocialMedia.googleChat.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.googleChat.splice(
                                  googleChatIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (
                        !updatedCompanies[index].companySocialMedia.googleChat
                      ) {
                        updatedCompanies[index].companySocialMedia.googleChat =
                          [];
                      }
                      updatedCompanies[
                        index
                      ].companySocialMedia.googleChat.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Google Chat Accounts
                  </MyText>

                  {/* discord, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.discord &&
                    company.companySocialMedia.discord.length > 0 &&
                    company.companySocialMedia.discord.map(
                      (discord, discordIndex) => (
                        <View key={discordIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={discordIndex}
                            label={
                              company.companySocialMedia.discord.length > 0
                                ? `Discord ${discordIndex + 1}`
                                : "Discord"
                            }
                            value={discord}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.discord[discordIndex] = val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.discord}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.discord}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("discord")
                            }
                          />
                          {company.companySocialMedia.discord.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.discord.splice(
                                  discordIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.discord) {
                        updatedCompanies[index].companySocialMedia.discord = [];
                      }
                      updatedCompanies[index].companySocialMedia.discord.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Discord Accounts
                  </MyText>

                  {/* wechat, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.wechat &&
                    company.companySocialMedia.wechat.length > 0 &&
                    company.companySocialMedia.wechat.map(
                      (wechat, wechatIndex) => (
                        <View key={wechatIndex} style={{ marginVertical: 8 }}>
                          <InputField
                            key={wechatIndex}
                            label={
                              company.companySocialMedia.wechat.length > 0
                                ? `WeChat ${wechatIndex + 1}`
                                : "WeChat"
                            }
                            value={wechat}
                            onChangeText={(val) => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[index].companySocialMedia.wechat[
                                wechatIndex
                              ] = val;
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            error={errors.wechat}
                            showVisibilityToggle={!ownProfileData}
                            isVisible={fieldVisibility.wechat}
                            onVisibilityToggle={() =>
                              handleVisibilityToggle("wechat")
                            }
                          />
                          {company.companySocialMedia.wechat.length > 1 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedCompanies = [...form.companyInfo];
                                updatedCompanies[
                                  index
                                ].companySocialMedia.wechat.splice(
                                  wechatIndex,
                                  1
                                );
                                handleChange("companyInfo", updatedCompanies);
                              }}
                              style={{
                                position: "absolute",
                                right: 5,
                                top: -15,
                                zIndex: 1,
                                padding: 10,
                              }}
                            >
                              <Image
                                source={icons.minusIcon}
                                resizeMode="contain"
                                style={{ width: 20, height: 20 }}
                              />
                            </TouchableOpacity>
                          )}
                        </View>
                      )
                    )}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.wechat) {
                        updatedCompanies[index].companySocialMedia.wechat = [];
                      }
                      updatedCompanies[index].companySocialMedia.wechat.push(
                        ""
                      );
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More WeChat Accounts
                  </MyText>

                  {/* kik, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.kik &&
                    company.companySocialMedia.kik.length > 0 &&
                    company.companySocialMedia.kik.map((kik, kikIndex) => (
                      <View key={kikIndex} style={{ marginVertical: 8 }}>
                        <InputField
                          key={kikIndex}
                          label={
                            company.companySocialMedia.kik.length > 0
                              ? `Kik ${kikIndex + 1}`
                              : "Kik"
                          }
                          value={kik}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].companySocialMedia.kik[
                              kikIndex
                            ] = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.kik}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.kik}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("kik")
                          }
                        />
                        {company.companySocialMedia.kik.length > 1 && (
                          <TouchableOpacity
                            onPress={() => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.kik.splice(kikIndex, 1);
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            style={{
                              position: "absolute",
                              right: 5,
                              top: -15,
                              zIndex: 1,
                              padding: 10,
                            }}
                          >
                            <Image
                              source={icons.minusIcon}
                              resizeMode="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.kik) {
                        updatedCompanies[index].companySocialMedia.kik = [];
                      }
                      updatedCompanies[index].companySocialMedia.kik.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Kik Accounts
                  </MyText>

                  {/* line, can be multiple */}
                  {company.companySocialMedia &&
                    company.companySocialMedia.line &&
                    company.companySocialMedia.line.length > 0 &&
                    company.companySocialMedia.line.map((line, lineIndex) => (
                      <View key={lineIndex} style={{ marginVertical: 8 }}>
                        <InputField
                          key={lineIndex}
                          label={
                            company.companySocialMedia.line.length > 0
                              ? `Line ${lineIndex + 1}`
                              : "Line"
                          }
                          value={line}
                          onChangeText={(val) => {
                            const updatedCompanies = [...form.companyInfo];
                            updatedCompanies[index].companySocialMedia.line[
                              lineIndex
                            ] = val;
                            handleChange("companyInfo", updatedCompanies);
                          }}
                          error={errors.line}
                          showVisibilityToggle={!ownProfileData}
                          isVisible={fieldVisibility.line}
                          onVisibilityToggle={() =>
                            handleVisibilityToggle("line")
                          }
                        />
                        {company.companySocialMedia.line.length > 1 && (
                          <TouchableOpacity
                            onPress={() => {
                              const updatedCompanies = [...form.companyInfo];
                              updatedCompanies[
                                index
                              ].companySocialMedia.line.splice(lineIndex, 1);
                              handleChange("companyInfo", updatedCompanies);
                            }}
                            style={{
                              position: "absolute",
                              right: 5,
                              top: -15,
                              zIndex: 1,
                              padding: 10,
                            }}
                          >
                            <Image
                              source={icons.minusIcon}
                              resizeMode="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    ))}
                  <MyText
                    underline
                    p
                    medium
                    style={{ color: colors.primary, marginBottom: 10 }}
                    onPress={() => {
                      const updatedCompanies = [...form.companyInfo];
                      if (!updatedCompanies[index].companySocialMedia) {
                        updatedCompanies[index].companySocialMedia = {};
                      }
                      if (!updatedCompanies[index].companySocialMedia.line) {
                        updatedCompanies[index].companySocialMedia.line = [];
                      }
                      updatedCompanies[index].companySocialMedia.line.push("");
                      handleChange("companyInfo", updatedCompanies);
                    }}
                  >
                    +Add More Line Accounts
                  </MyText>
                </View>
              )}
            </View>
          ))}
      </View>
    );
  }
);

BusinessSection.propTypes = {
  form: PropTypes.shape({
    // Business Information
    jobTitle: PropTypes.string,
    company: PropTypes.string,
    department: PropTypes.string,
    industry: PropTypes.string,
    workHours: PropTypes.string,
    companyWebsite: PropTypes.string,
    businessLinkedIn: PropTypes.string,
    businessTwitter: PropTypes.string,
    companyLogo: PropTypes.string,

    // Business Address
    officeBuilding: PropTypes.string,
    businessStreet: PropTypes.string,
    businessCity: PropTypes.string,
    businessState: PropTypes.string,
    businessZipCode: PropTypes.string,
    businessCountry: PropTypes.string,

    // Business Details
    workEmail: PropTypes.string,
    workPhone: PropTypes.string,
    workFax: PropTypes.string,
    resume: PropTypes.string,
    certifications: PropTypes.string,
    professionalNotes: PropTypes.string,

    // Account Details
    accountName: PropTypes.string,
    bankName: PropTypes.string,
    accountNumber: PropTypes.string,
    ifscCode: PropTypes.string,
    paypalEmail: PropTypes.string,

    // Billing Address
    billingApartment: PropTypes.string,
    billingStreet: PropTypes.string,
    billingCity: PropTypes.string,
    billingState: PropTypes.string,
    billingZipCode: PropTypes.string,
    billingCountry: PropTypes.string,

    // Card Details
    cardName: PropTypes.string,
    cardNumber: PropTypes.string,
    cardExpiry: PropTypes.string,
    cardCvv: PropTypes.string,

    // Company Messenger IDs
    iMessage: PropTypes.string,
    googleChat: PropTypes.string,
    discord: PropTypes.string,
    slack: PropTypes.string,
    wechat: PropTypes.string,
    kik: PropTypes.string,
    line: PropTypes.string,

    // Company Social Media
    facebook: PropTypes.string,
    instagram: PropTypes.string,
    twitter: PropTypes.string,
    linkedIn: PropTypes.string,
    snapchat: PropTypes.string,
    whatsapp: PropTypes.string,
    telegram: PropTypes.string,
    signal: PropTypes.string,
    skype: PropTypes.string,
    youtube: PropTypes.string,
    twitch: PropTypes.string,
    tiktok: PropTypes.string,
  }).isRequired,
  handleChange: PropTypes.func.isRequired,
  errors: PropTypes.object,
};

export default BusinessSection;
