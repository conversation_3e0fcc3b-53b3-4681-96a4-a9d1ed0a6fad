import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Alert,
  Image,
  FlatList,
} from "react-native";
import Header from "../../components/Header";
import InputField from "../../components/InputField";
import icons from "../../assets/icons";
import MyText from "../../components/MyText";
import ContactCard from "../../components/ContactCard";
import { useDispatch, useSelector } from "react-redux";
import {
  createTag,
  deleteTag,
  editTag,
  getTagByID,
  setTagsData,
} from "../../redux/features/mainSlice";
import { showToast } from "../../utils/toastConfig";
import AppLoader from "../../components/AppLoader";

const NewTagScreen = ({ navigation, route }) => {
  const [title, setTitle] = useState("");
  const [contacts, setContacts] = useState([]);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const tagsData = useSelector((state) => state.mainSlice.tagsData);

  const dispatch = useDispatch();

  useEffect(() => {
    if (route?.params?.id) {
      fetchTagFromId(route.params.id);
    }
  }, [route?.params?.contacts]);

  const fetchTagFromId = async (id) => {
    try {
      setLoading(true);
      const response = await dispatch(getTagByID(id));
      console.log("Tag details:", response);
      if (response.payload.success) {
        const tag = response.payload.data;
        setTitle(tag.tag_name);
        const tempContacts = tag.members.map((member) => ({
          id: member._id,
          name: `${member.firstName} ${member.middleName} ${member.lastName}`,
          phone: member.phoneNumbers[0]?.number || "",
          profile_image: member.profile_image,
        }));
        console.log(
          "🚀 ~ tempContacts ~ tempContacts:",
          JSON.stringify(tempContacts, null, 2)
        );
        // setContacts(tempContacts);
        dispatch(
          setTagsData({
            title: tag.tag_name,
            contacts: tempContacts,
          })
        );
      } else {
        console.error("Failed to fetch tag:", response.payload.data.message);
      }
    } catch (error) {
      console.error("Error fetching tag:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddContacts = () => {
    Keyboard.dismiss();
    navigation.navigate("AddContactsScreen");
  };

  const validateForm = () => {
    const newErrors = {};
    if (!title.trim()) newErrors.title = "Title is required";
    if (tagsData?.contacts?.length === 0)
      newErrors.contacts = "Please add at least one contact";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateTag = async () => {
    if (!validateForm()) {
      return;
    }
    try {
      const payload = {
        tag_name: title,
        addInTag: tagsData?.contacts.map((contact) => ({
          memberId: contact.id,
        })),
      };

      const response = await dispatch(createTag(payload));
      console.log("Tag creation response:", response);

      if (response.payload.success) {
        showToast("success", "Tag created successfully");
        // setContacts([]);
        // setTitle('');
        navigation.goBack();
      } else {
        showToast(
          "error",
          response.payload.data.message || "Failed to create tag"
        );
      }
    } catch (error) {
      console.error("Error creating tag:", error);
      Alert.alert("Error", "Failed to create tag. Please try again.");
    }
  };

  const handleUpdateTag = async () => {
    if (!validateForm()) {
      return;
    }
    try {
      const payload = {
        tag_name: title,
        addInTag: tagsData?.contacts.map((contact) => ({
          memberId: contact.id,
        })),
      };

      const response = await dispatch(
        editTag({ id: route.params.id, data: payload })
      );
      console.log("Tag update response:", response);

      if (response.payload.success) {
        showToast("success", "Tag updated successfully");
        navigation.goBack();
      } else {
        showToast(
          "error",
          response.payload.data.message || "Failed to update tag"
        );
      }
    } catch (error) {
      console.error("Error updating tag:", error);
      Alert.alert("Error", "Failed to update tag. Please try again.");
    }
  };

  const handleDeleteTag = async () => {
    Alert.alert(
      "Delete Tag",
      "Are you sure you want to delete this tag?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          onPress: async () => {
            try {
              const response = await dispatch(deleteTag(route.params.id));
              console.log("Tag deletion response:", JSON.stringify(response));

              if (response.payload.success) {
                showToast("success", "Tag deleted successfully");
                navigation.goBack();
              } else {
                showToast(
                  "error",
                  response.payload.data.message || "Failed to delete tag"
                );
              }
            } catch (error) {
              console.error("Error deleting tag:", error);
              Alert.alert("Error", "Failed to delete tag. Please try again.");
            }
          },
        },
      ],
      { cancelable: false }
    );
  };

  // console.log("Tags Data:", JSON.stringify(tagsData.contacts));

  return (
    <View style={styles.container}>
      <Header
        title={route?.params?.id ? "Edit Tag" : "Create Tag"}
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />

      <View style={styles.scroll}>
        <InputField
          label="Title*"
          placeholder="Enter Title"
          value={title}
          onChangeText={(val) => {
            setTitle(val);
            setErrors((prev) => ({ ...prev, title: "" }));
          }}
          error={errors.title}
        />

        <MyText style={styles.label}>Contacts</MyText>

        {tagsData?.contacts?.length > 0 ? (
          <View style={{ height: "68%" }}>
            <FlatList
              data={tagsData.contacts}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <ContactCard
                  name={item.name}
                  phone={item.phone}
                  imgUrl={item.profile_image}
                  isSelected={tagsData.contacts.includes(item)}
                  mode="cancel"
                  onPress={() => toggleSelect(item)}
                  onCancel={async () => {
                    console.log(
                      "Cancel pressed",
                      item,
                      JSON.stringify(tagsData.contacts)
                    );
                    const updatedContacts = tagsData.contacts.filter(
                      (contact) => contact.id !== item.id
                    );
                    await dispatch(
                      setTagsData({
                        ...tagsData,
                        contacts: updatedContacts,
                      })
                    );
                  }}
                />
              )}
              contentContainerStyle={{}}
              ListFooterComponent={
                <TouchableOpacity
                  style={[styles.arrowWrapper, { alignSelf: "flex-end" }]}
                  onPress={handleAddContacts}
                >
                  <MyText bold style={[styles.contactText]}>
                    {"+Add More"}
                  </MyText>
                </TouchableOpacity>
              }
            />
          </View>
        ) : (
          <TouchableOpacity
            style={[styles.contactRow, errors.contacts && styles.errorBorder]}
            onPress={handleAddContacts}
          >
            <MyText style={[styles.contactText]}>{"Add Contacts"}</MyText>
            <Image
              source={icons.rightArrowIcon}
              resizeMode="contain"
              style={styles.arrow}
            />
          </TouchableOpacity>
        )}

        {errors.contacts && (
          <MyText style={styles.errorText}>{errors.contacts}</MyText>
        )}
      </View>
      <TouchableOpacity
        style={styles.createButton}
        onPress={route?.params?.id ? handleUpdateTag : handleCreateTag}
      >
        <MyText style={styles.buttonText}>
          {route?.params?.id ? "Update Tag" : "Create Tag"}
        </MyText>
      </TouchableOpacity>

      {route?.params?.id && (
        <TouchableOpacity
          style={[styles.createButton, { backgroundColor: "red", bottom: 20 }]}
          onPress={handleDeleteTag}
        >
          <MyText style={styles.buttonText}>{"Delete Tag"}</MyText>
        </TouchableOpacity>
      )}

      <AppLoader isLoading={loading} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: {
    padding: 20,
  },
  label: {
    fontWeight: "600",
    marginBottom: 6,
  },
  contactRow: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f2f2f2",
    borderRadius: 10,
    padding: 15,
    justifyContent: "space-between",
    marginBottom: 8,
  },
  contactText: {
    color: "#888",
    fontSize: 13,
  },
  selected: {
    color: "#000",
  },
  arrowWrapper: {
    padding: 5,
  },
  arrow: {
    height: 14,
    width: 14,
  },
  errorBorder: {
    borderWidth: 1,
    borderColor: "red",
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 2,
    marginBottom: 12,
  },
  createButton: {
    marginTop: 20,
    backgroundColor: "#2E64FE",
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
    bottom: 80,
    left: 20,
    right: 20,
    position: "absolute",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});

export default NewTagScreen;
