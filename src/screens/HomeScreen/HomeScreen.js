import React, { useState, useCallback, useEffect, useMemo } from "react";
import { View, StyleSheet } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Components
import Header from "../../components/Header";
import SearchBar from "../../components/SearchBar";
import AppLoader from "../../components/AppLoader";
import AppModal from "../../components/AppModal";
import SortOptionsBox from "../../components/SortOptionsBox";
import BottomModal from "../../components/BottomModal";
import FloatingPlusButton from "../../components/FloatingPlusButton";
import {
  FindingDuplicates,
  ProfileTagChips,
  ImportOptionsList,
  ContactsSection,
} from "./components";

// Assets & Utils
import icons from "../../assets/icons";
import { fetchContactsFromSource } from "../../utils/contactHelpers";
import {
  filterOptions,
  importOptions,
  sortOptions,
} from "../../utils/constants";

// Redux
import { getProfile } from "../../redux/features/authSlice";
import { getContacts, getDuplicates } from "../../redux/features/contactSlice";
import {
  getPersonalProfile,
  getProfileNameData,
} from "../../redux/features/SharingProfileSlice";
import { getTags } from "../../redux/features/mainSlice";

const HomeScreen = () => {
  // --- Navigation & Redux ---
  const navigation = useNavigation();
  const dispatch = useDispatch();

  // --- State ---
  const [isModalVisible, setModalVisible] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSortBoxVisible, setSortBoxVisible] = useState(false);
  const [isSecondBoxVisible, setSecondBoxVisible] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [selectedTag, setSelectedTag] = useState(null);
  const [activeFilter, setActiveFilter] = useState(null);
  const [activeSort, setActiveSort] = useState("az");
  const [prevContactsLength, setPrevContactsLength] = useState(null);

  // const showModal = useSelector(
  //   (state) => state.mainSlice.showModal
  // );

  // --- Selectors ---
  const user = useSelector((state) => state.auth.user);
  const contacts = useSelector((state) => state.contactSlice.getContacts);
  // console.log("🚀 ~ HomeScreen ~ contacts:", JSON.stringify(contacts, null, 2));
  const duplicatesState = useSelector(
    (state) => state.contactSlice.getDuplicatesState
  );
  const tagsData = useSelector((state) => state.mainSlice.tagsData);
  const personalProfileState = useSelector(
    (state) => state.sharingProfileSlice.getPersonalProfile
  );
  const getProfileNameDataState = useSelector(
    (state) => state.sharingProfileSlice.getProfileNameData
  );
  const { data: personalProfiledata } = personalProfileState;
  const { data: allProfileNameData } = getProfileNameDataState;
  const isPersonalProfileComplete =
    personalProfiledata?.data?.userInfo?.is_profile_complete;

  // --- Memoized Options ---
  const profileOptions = useMemo(() => {
    if (allProfileNameData?.data?.result?.length) {
      return allProfileNameData.data.result.map((item) => ({
        label: item.profile_name,
        value: item.profile_name,
        contacts: item.contacts,
      }));
    }
    return [];
  }, [allProfileNameData]);

  const tagOptions = useMemo(() => {
    if (tagsData && Array.isArray(tagsData.tags)) {
      return tagsData.tags.map((tag) => ({
        label: tag.tag_name,
        value: tag.tag_name,
        contacts: tag.contacts || tag.members || [],
      }));
    } else if (tagsData && Array.isArray(tagsData.result)) {
      return tagsData.result.map((tag) => ({
        label: tag.tag_name,
        value: tag.tag_name,
        contacts: tag.contacts || tag.members || [],
      }));
    }
    return [];
  }, [tagsData]);

  // --- Derived Contacts ---
  const profileContacts = useMemo(() => {
    if (selectedProfile) {
      const found = profileOptions.find((opt) => opt.value === selectedProfile);
      return found ? found.contacts : [];
    }
    return [];
  }, [selectedProfile, profileOptions]);

  const tagContacts = useMemo(() => {
    if (selectedTag) {
      const found = tagOptions.find((opt) => opt.value === selectedTag);
      return found ? found.contacts : [];
    }
    return [];
  }, [selectedTag, tagOptions]);

  const filteredContacts = useMemo(() => {
    let baseContacts;
    if (activeFilter === "profile" && selectedProfile) {
      baseContacts = profileContacts;
    } else if (activeFilter === "tag" && selectedTag) {
      baseContacts = tagContacts;
    } else if (activeFilter === "favourites") {
      // Only show contacts where is_favorite is true
      baseContacts = (contacts?.data?.data?.result || []).filter(
        (contact) => contact.is_favorite === true
      );
    } else {
      baseContacts = contacts?.data?.data?.result || [];
    }
    if (!searchQuery) {
      return baseContacts;
    } else if (baseContacts.length) {
      const search = searchQuery.trim().toLowerCase();
      const filtered = baseContacts.filter((contact) => {
        const name = `${contact.firstName || ""} ${contact.middleName || ""} ${
          contact.lastName || ""
        }`.toLowerCase();
        const emails = Array.isArray(contact.emails)
          ? contact.emails.map((e) => e.address?.toLowerCase() || "").join(" ")
          : "";
        const phones = Array.isArray(contact.phoneNumbers)
          ? contact.phoneNumbers
              .map((p) => p.number?.toLowerCase() || "")
              .join(" ")
          : "";
        return (
          name.includes(search) ||
          emails.includes(search) ||
          phones.includes(search)
        );
      });
      return filtered;
    }
    return [];
  }, [
    contacts,
    searchQuery,
    selectedProfile,
    profileContacts,
    activeFilter,
    selectedTag,
    tagContacts,
    activeSort,
  ]);

  // --- Memoized Handlers ---
  const handleSearchChange = useCallback((text) => {
    setSearchQuery(text);
  }, []);

  const handleSortIconPress = useCallback(() => {
    setSecondBoxVisible(false);
    setSortBoxVisible((prev) => !prev);
  }, []);

  const handleSortOption = useCallback(
    (option) => {
      setSortBoxVisible(false);
      if (!option) {
        setActiveSort("az"); // Set to A-Z when deselecting
        dispatch(getContacts());
        return;
      }
      setActiveSort(option.value);
      const params = option.value === "recent" ? { sort: -1 } : {};
      dispatch(getContacts(params));
    },
    [dispatch]
  );

  const handleSecondRightIconPress = useCallback(() => {
    setSortBoxVisible(false);
    setSecondBoxVisible((prev) => !prev);
  }, []);

  const handleSecondOption = useCallback(
    (option) => {
      setSecondBoxVisible(false);
      if (!option) {
        setActiveFilter(null);
        setSelectedProfile(null);
        setSelectedTag(null);
        dispatch(getContacts());
        return;
      }

      const value = option.value?.toLowerCase();
      const resetSelections = () => {
        setSelectedProfile(null);
        setSelectedTag(null);
      };

      switch (value) {
        case "profile":
          if (activeFilter === "profile") {
            setActiveFilter(null);
            setSelectedProfile(null);
            dispatch(getContacts());
          } else {
            setActiveFilter("profile");
          }
          break;
        case "city":
          setActiveFilter("city");
          resetSelections();
          dispatch(getContacts({ filter: "city" }));
          break;
        case "favourites":
          setActiveFilter("favourites");
          resetSelections();
          dispatch(getContacts({ isFavourite: true }));
          break;
        case "tag":
          if (activeFilter === "tag") {
            setActiveFilter(null);
            setSelectedTag(null);
            dispatch(getContacts());
          } else {
            setActiveFilter("tag");
          }
          break;
        default:
          setActiveFilter(option.value || null);
          resetSelections();
          dispatch(getContacts());
      }
    },
    [activeFilter, dispatch]
  );

  const handleAddManualContact = useCallback(() => {
    navigation.navigate("AddManualContactScreen");
  }, [navigation]);

  const handleProfileModalSubmit = useCallback(async () => {
    setShowProfileModal(false);
    await AsyncStorage.setItem(`profileModalSubmitted_${user?.id}`, "true");
    setTimeout(() => {
      navigation.navigate("ProfileCompletionScreen", { profileId: user?._id });
    }, 100);
  }, [user?.id, user?._id, navigation]);

  const handleFetchContacts = useCallback(
    async (source) => {
      setModalVisible(false);
      const onSuccess = (contactsData, sourceType) => {
        navigation.navigate("ImportContactScreen", {
          contacts: contactsData,
          source: sourceType,
        });
      };
      const onError = (error) => {
        console.error("Error fetching contacts:", error);
      };
      await fetchContactsFromSource(source, onSuccess, onError, setIsLoading);
    },
    [navigation]
  );

  // --- Optimized Effects ---
  const fetchInitialData = useCallback(() => {
    dispatch(getProfile());
    dispatch(getContacts());
    dispatch(getPersonalProfile());
    dispatch(getProfileNameData());
    dispatch(getTags());
  }, [dispatch]);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  useFocusEffect(
    useCallback(() => {
      // Only refetch contacts and profile data on focus
      dispatch(getContacts());
      dispatch(getPersonalProfile());
    }, [dispatch])
  );

  const checkProfileModal = useCallback(async () => {
    if (!user?.id) return;

    try {
      const submitted = await AsyncStorage.getItem(
        `profileModalSubmitted_${user.id}`
      );
      const hasSubmitted = submitted === "true";

      if (__DEV__) {
        console.log("Profile modal check:", {
          submitted,
          hasSubmitted,
          isPersonalProfileComplete,
        });
      }

      if (!isPersonalProfileComplete && !hasSubmitted) {
        setShowProfileModal(true);
      }
    } catch (error) {
      console.error("Error checking profile modal:", error);
    }
  }, [user?.id, isPersonalProfileComplete]);

  useEffect(() => {
    checkProfileModal();
  }, [checkProfileModal]);

  // Optimized duplicates check
  useEffect(() => {
    const currentLength = contacts?.data?.data?.result?.length || 0;

    if (prevContactsLength !== currentLength) {
      setPrevContactsLength(currentLength);

      // Only fetch duplicates if we have contacts
      if (currentLength > 0) {
        dispatch(getDuplicates());
      }
    }
  }, [contacts?.data?.data?.result?.length, dispatch, prevContactsLength]);

  // console.log(filteredContacts, "filteredContacts");

  // --- Render ---
  return (
    <View style={styles.container}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        <Header
          title={`Welcome\nBack, ${user?.firstName}`}
          leftIcon={icons?.DrawerIcon}
          onPressLeft={() => navigation.openDrawer()}
          onPressRight2={() =>
            navigation.navigate("ContactDetailsScreen", {
              id: user?._id,
              personal: true,
            })
          }
          rightIcon1={icons?.notificationIcon}
          isAvatar
          pb={50}
        />
        <View style={styles.searchBarContainer}>
          <SearchBar
            placeholder="Search here"
            value={searchQuery}
            onChangeText={handleSearchChange}
            rightIcon={icons.filterIcon}
            onPressRightIcon1={handleSortIconPress}
            onPressRightIcon2={handleSecondRightIconPress}
          />
          {isSortBoxVisible && (
            <SortOptionsBox
              options={sortOptions}
              onSelect={handleSortOption}
              style={[styles.sortBoxOverlay, { right: 60 }]}
              optionStyle={styles.sortBoxOption}
              optionTextStyle={styles.optionText}
              activeValue={activeSort}
              allowDeselect={true}
            />
          )}
          {isSecondBoxVisible && (
            <SortOptionsBox
              options={filterOptions}
              onSelect={handleSecondOption}
              style={[styles.sortBoxOverlay, { right: 20 }]}
              optionStyle={styles.sortBoxOption}
              optionTextStyle={styles.optionText}
              activeValue={activeFilter}
              allowDeselect={true}
            />
          )}
        </View>
      </View>
      {/* Profile Modal */}
      <AppModal
        visible={showProfileModal}
        title="Complete Your Profile"
        description="Please complete your profile information to get started."
        btnTitle="Continue"
        onSubmit={handleProfileModalSubmit}
        onClose={() => setShowProfileModal(false)}
        containerStyle={{ zIndex: 2000 }}
      />
      {/* Loader */}
      <AppLoader isLoading={isLoading || contacts.loading} />
      {/* Duplicates Section */}
      <View style={{ marginTop: 22 }}>
        <FindingDuplicates
          isLoading={duplicatesState?.loading}
          duplicatesCount={
            duplicatesState?.data?.data?.totalDuplicateGroups ?? 0
          }
          onViewPress={() => {
            navigation.navigate("DuplicateContactScreen", {
              onRefetch: () => {
                dispatch(getContacts());
                dispatch(getDuplicates());
              },
            });
          }}
        />
      </View>
      {/* Profile/Tag Chips */}
      <ProfileTagChips
        activeFilter={activeFilter}
        profileOptions={profileOptions}
        tagOptions={tagOptions}
        selectedProfile={selectedProfile}
        setSelectedProfile={setSelectedProfile}
        selectedTag={selectedTag}
        setSelectedTag={setSelectedTag}
      />
      {/* Main Content */}
      <View style={styles.mainContentContainer}>
        <View style={styles.listContainer}>
          <ContactsSection
            filteredContacts={filteredContacts}
            contacts={contacts}
            selectedProfile={selectedProfile}
            selectedTag={selectedTag}
            activeFilter={activeFilter}
            activeSort={activeSort}
            user={user}
            navigation={navigation}
            onImportPress={() => setModalVisible(true)}
          />
        </View>
      </View>
      {/* Floating Add Button */}
      <FloatingPlusButton onPress={handleAddManualContact} />
      {/* Import Modal */}
      <BottomModal
        isVisible={isModalVisible}
        onClose={() => setModalVisible(false)}
        title="Import Contacts From"
      >
        <ImportOptionsList
          importOptions={importOptions}
          handleFetchContacts={handleFetchContacts}
        />
      </BottomModal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerSection: {
    position: "relative",
    zIndex: 10010,
  },
  mainContentContainer: {
    flex: 1,
    zIndex: 1,
  },
  searchBarContainer: {
    position: "absolute",
    bottom: -20,
    left: 0,
    right: 0,
    paddingHorizontal: 15,
    zIndex: 10020,
  },
  listContainer: {
    flex: 1,
    paddingTop: 15,
    zIndex: 1,
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});

export default HomeScreen;
