import React from "react";
import { View } from "react-native";
import MyText from "../../../components/MyText";
import ContactList from "../../../components/ContactList";
import NoContactsImported from "./NoContactsImported";
import { PrimaryButton } from "../../../components/Button";
import MyCard from "./MyCard";
import ProfileTagChips from "./ProfileTagChips";
import { groupContactsByAlphabet } from "../../../utils/commonHelpers";
import colors from "../../../assets/colors";

const ContactsSection = React.memo(
  ({
    filteredContacts,
    contacts,
    selectedProfile,
    selectedTag,
    activeFilter,
    activeSort,
    user,
    navigation,
    onImportPress,
  }) => {
    const contactsCount = contacts?.data?.data?.pagination?.total || 0;
    const displayTitle = selectedProfile
      ? `${selectedProfile} Contacts`
      : // : `Contacts (${contactsCount})`;
        `Contacts`;

    if (filteredContacts.length > 0) {
      return (
        <>
          <MyText
            semibold
            h6
            children={displayTitle}
            style={styles.contactsHeader}
          />
          <MyCard
            name={user?.firstName}
            imageUri={user?.profileImage}
            onPress={() => navigation.navigate("MyCardScreen")}
          />
          <ContactList
            contacts={
              activeSort === "az" || activeSort === "za"
                ? groupContactsByAlphabet(filteredContacts, activeSort)
                : filteredContacts
            }
            showAlphabetList={activeSort === "az" || activeSort === "za"}
            mode="viewprofile"
            reverseAlphabet={activeSort === "za"}
            ListHeaderComponent={<ProfileTagChips />}
          />
        </>
      );
    }

    // Handle filtered empty states
    if (
      activeFilter &&
      ((activeFilter === "profile" && selectedProfile) ||
        (activeFilter === "tag" && selectedTag) ||
        activeFilter === "favourites")
    ) {
      return (
        <View style={styles.emptyFilteredState}>
          <MyText
            p
            children={
              activeFilter === "favourites"
                ? "No favourite contact is there."
                : `No contacts found in the ${
                    activeFilter === "profile" ? selectedProfile : selectedTag
                  } ${activeFilter}.`
            }
          />
        </View>
      );
    }

    // Default empty state
    return (
      <>
        <NoContactsImported />
        <PrimaryButton
          onPress={onImportPress}
          title="Import Contacts"
          style={styles.button}
        />
      </>
    );
  }
);

const styles = {
  contactsHeader: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  button: {
    backgroundColor: colors.black,
    width: "50%",
    marginTop: 10,
    alignSelf: "center",
  },
  emptyFilteredState: {
    alignItems: "center",
    marginTop: 40,
  },
};

export default ContactsSection;
