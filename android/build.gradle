    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"

        Msal_compileSdkVersion = 35
        Msal_targetSdkVersion  = 35
        Msal_minSdkVersion     = 24
    }
buildscript {

    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        // classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath("com.android.tools.build:gradle:8.4.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:2.0.21")
    }
}


allprojects {
    repositories {
        google()
        mavenCentral()
        // Microsoft’s Duo SDK feed (for display-mask)
        maven {
            url "https://pkgs.dev.azure.com/MicrosoftDeviceSDK/DuoSDK-Public/_packaging/Duo-SDK-Feed%40Local/maven/v1"
        }
    }
}

apply plugin: "com.facebook.react.rootproject"